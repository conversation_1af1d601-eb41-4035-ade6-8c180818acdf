{"type": "FeatureCollection", "name": "comprehensive_lanes", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}, "features": [{"type": "Feature", "id": "laneGroup_999999_eb1e2b3d", "momType": "laneGroup", "referencePoint": {"type": "Point", "coordinates": [0.0, 0.0]}, "geometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [0.01, 0.0], [0.02, 0.0]]}, "properties": {"@ns:com:here:mom:meta": {"modelVersion": "1.0", "sourceId": "osm_las_processor", "lastUpdatedBy": "precise_las_to_lane_geojson", "updatedByUser": "system"}, "@ns:com:here:xyz": {"tags": ["motorway", "lane_group", "osm_derived"]}, "featureType": "laneGroup", "lengthInCm": 2, "startLaneGroupConnectorId": "connector_start_999999", "endLaneGroupConnectorId": "connector_end_999999", "referenceGeometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [0.01, 0.0], [0.02, 0.0]]}, "leftBoundaryGeometry": {"type": "LineString", "coordinates": [[0.0, 9e-05], [0.01, 9e-05], [0.02, 9e-05]]}, "rightBoundaryGeometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [0.01, 0.0], [0.02, 0.0]]}, "laneBoundaries": [{"laneBoundaryId": "boundary_left_1_1519c0da", "geometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [0.01, 0.0], [0.02, 0.0]]}, "confidence": {"simpleScores": [{"scoreType": "detection", "score": 0.85, "featureType": "laneBoundary", "updateSource": "osm_derived"}]}, "laneBoundaryAttributes": {"roadBoundaryType": [{"roadBoundaryType": "solid", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "laneBoundaryTraversal": [{"laneBoundaryTraversal": "allowed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "adjacentLaneGroups": []}, "parallelElements": {"sequentialElements": [{"range": {"startOffset": 0, "endOffset": 2}, "stripeDetail": {"color": "white", "material": "paint", "styleType": "solid", "widthInCentimeters": 10}, "laneBoundaryTraversal": "allowed"}]}}, {"laneBoundaryId": "boundary_left_2_d8aaa5c0", "geometry": {"type": "LineString", "coordinates": [[0.0, 3e-05], [0.01, 3e-05], [0.02, 3e-05]]}, "confidence": {"simpleScores": [{"scoreType": "detection", "score": 0.85, "featureType": "laneBoundary", "updateSource": "osm_derived"}]}, "laneBoundaryAttributes": {"roadBoundaryType": [{"roadBoundaryType": "dashed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "laneBoundaryTraversal": [{"laneBoundaryTraversal": "allowed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "adjacentLaneGroups": []}, "parallelElements": {"sequentialElements": [{"range": {"startOffset": 0, "endOffset": 2}, "stripeDetail": {"color": "white", "material": "paint", "styleType": "dashed", "widthInCentimeters": 10}, "laneBoundaryTraversal": "allowed"}]}}, {"laneBoundaryId": "boundary_left_3_d7f2ec98", "geometry": {"type": "LineString", "coordinates": [[0.0, 6e-05], [0.01, 6e-05], [0.02, 6e-05]]}, "confidence": {"simpleScores": [{"scoreType": "detection", "score": 0.85, "featureType": "laneBoundary", "updateSource": "osm_derived"}]}, "laneBoundaryAttributes": {"roadBoundaryType": [{"roadBoundaryType": "dashed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "laneBoundaryTraversal": [{"laneBoundaryTraversal": "allowed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "adjacentLaneGroups": []}, "parallelElements": {"sequentialElements": [{"range": {"startOffset": 0, "endOffset": 2}, "stripeDetail": {"color": "white", "material": "paint", "styleType": "dashed", "widthInCentimeters": 10}, "laneBoundaryTraversal": "allowed"}]}}, {"laneBoundaryId": "boundary_left_4_621bf7fd", "geometry": {"type": "LineString", "coordinates": [[0.0, 9e-05], [0.01, 9e-05], [0.02, 9e-05]]}, "confidence": {"simpleScores": [{"scoreType": "detection", "score": 0.85, "featureType": "laneBoundary", "updateSource": "osm_derived"}]}, "laneBoundaryAttributes": {"roadBoundaryType": [{"roadBoundaryType": "dashed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "laneBoundaryTraversal": [{"laneBoundaryTraversal": "allowed", "boundaryRange": {"startOffset": 0, "endOffset": 2}}], "adjacentLaneGroups": []}, "parallelElements": {"sequentialElements": [{"range": {"startOffset": 0, "endOffset": 2}, "stripeDetail": {"color": "white", "material": "paint", "styleType": "dashed", "widthInCentimeters": 10}, "laneBoundaryTraversal": "allowed"}]}}], "lanes": [{"startLaneConnectorId": "connector_start_lane_1_d63fc64b", "endLaneConnectorId": "connector_end_lane_1_d58a16bf", "leftLaneBoundaryId": "boundary_left_1", "rightLaneBoundaryId": "boundary_right_1", "lengthInCm": 2, "directionOfTravel": "forward", "drivePathGeometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [0.01, 0.0], [0.02, 0.0]]}, "laneAttributes": {"laneTypes": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": false, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "speedLimits": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "value": 120, "unit": "kmh", "isUnlimited": false}], "transitions": [], "intersections": [], "laneAccesses": [], "stoppingLocations": [], "directionCategoryMarkers": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "directionCategoryMarkers": "through"}]}, "laneParametericAttributes": {"parametricLaneType": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": false, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "parametricSpeedLimit": [], "parametricTransitionStatus": []}, "roadReferences": [{"topologySegmentRef": {"id": "segment_999999", "referencePoint": {"type": "Point", "coordinates": [0.0, 0.0]}}, "topologySegmentRange": {"startOffset": 0, "endOffset": 2}, "sourceRange": {"startOffset": 0, "endOffset": 2}}]}, {"startLaneConnectorId": "connector_start_lane_2_19a320f7", "endLaneConnectorId": "connector_end_lane_2_773260a3", "leftLaneBoundaryId": "boundary_left_2", "rightLaneBoundaryId": "boundary_right_2", "lengthInCm": 2, "directionOfTravel": "forward", "drivePathGeometry": {"type": "LineString", "coordinates": [[0.0, 3e-05], [0.01, 3e-05], [0.02, 3e-05]]}, "laneAttributes": {"laneTypes": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": false, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "speedLimits": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "value": 120, "unit": "kmh", "isUnlimited": false}], "transitions": [], "intersections": [], "laneAccesses": [], "stoppingLocations": [], "directionCategoryMarkers": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "directionCategoryMarkers": "through"}]}, "laneParametericAttributes": {"parametricLaneType": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": false, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "parametricSpeedLimit": [], "parametricTransitionStatus": []}, "roadReferences": [{"topologySegmentRef": {"id": "segment_999999", "referencePoint": {"type": "Point", "coordinates": [0.0, 3e-05]}}, "topologySegmentRange": {"startOffset": 0, "endOffset": 2}, "sourceRange": {"startOffset": 0, "endOffset": 2}}]}, {"startLaneConnectorId": "connector_start_lane_3_3f267bb0", "endLaneConnectorId": "connector_end_lane_3_d98740ef", "leftLaneBoundaryId": "boundary_left_3", "rightLaneBoundaryId": "boundary_right_3", "lengthInCm": 2, "directionOfTravel": "forward", "drivePathGeometry": {"type": "LineString", "coordinates": [[0.0, 6e-05], [0.01, 6e-05], [0.02, 6e-05]]}, "laneAttributes": {"laneTypes": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": false, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "speedLimits": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "value": 120, "unit": "kmh", "isUnlimited": false}], "transitions": [], "intersections": [], "laneAccesses": [], "stoppingLocations": [], "directionCategoryMarkers": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "directionCategoryMarkers": "through"}]}, "laneParametericAttributes": {"parametricLaneType": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": false, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "parametricSpeedLimit": [], "parametricTransitionStatus": []}, "roadReferences": [{"topologySegmentRef": {"id": "segment_999999", "referencePoint": {"type": "Point", "coordinates": [0.0, 6e-05]}}, "topologySegmentRange": {"startOffset": 0, "endOffset": 2}, "sourceRange": {"startOffset": 0, "endOffset": 2}}]}, {"startLaneConnectorId": "connector_start_lane_4_0d09b4e6", "endLaneConnectorId": "connector_end_lane_4_d09419ba", "leftLaneBoundaryId": "boundary_left_4", "rightLaneBoundaryId": "boundary_right_4", "lengthInCm": 2, "directionOfTravel": "forward", "drivePathGeometry": {"type": "LineString", "coordinates": [[0.0, 9e-05], [0.01, 9e-05], [0.02, 9e-05]]}, "laneAttributes": {"laneTypes": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": true, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "speedLimits": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "value": 120, "unit": "kmh", "isUnlimited": false}], "transitions": [], "intersections": [], "laneAccesses": [], "stoppingLocations": [], "directionCategoryMarkers": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "directionCategoryMarkers": "left"}]}, "laneParametericAttributes": {"parametricLaneType": [{"laneRange": {"startOffset": 0, "endOffset": 2}, "laneType": {"regular": false, "auxiliary": false, "acceleration": false, "deceleration": false, "bicycle": false, "bus": false, "centreTurn": false, "drivableParking": false, "drivableShoulder": false, "express": true, "hov": false, "onStreetParking": false, "other": false, "parking": false, "passing": false, "regulatedAccess": false, "reversible": false, "shoulder": false, "slow": false, "truckParking": false, "turn": true, "undefined": false, "variableDriving": false}}], "laneWidthProfile": {"startWidthCm": 350, "endWidthCm": 350, "minWidthCm": 350, "maxWidthCm": 350, "minWidthLocation": 0, "maxWidthLocation": 0}, "parametricSpeedLimit": [], "parametricTransitionStatus": []}, "roadReferences": [{"topologySegmentRef": {"id": "segment_999999", "referencePoint": {"type": "Point", "coordinates": [0.0, 9e-05]}}, "topologySegmentRange": {"startOffset": 0, "endOffset": 2}, "sourceRange": {"startOffset": 0, "endOffset": 2}}]}], "incomingLaneGroups": [], "outgoingLaneGroups": [], "roadReferences": [{"topologySegmentRef": {"id": "segment_999999", "referencePoint": {"type": "Point", "coordinates": [0.0, 0.0]}}, "topologySegmentRange": {"startOffset": 0, "endOffset": 2}, "sourceRange": {"startOffset": 0, "endOffset": 2}}]}}]}