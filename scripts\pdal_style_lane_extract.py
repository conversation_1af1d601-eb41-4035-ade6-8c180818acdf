#!/usr/bin/env python3
"""
PDAL-style Lane Paint Extraction Pipeline
==========================================

This script implements the lane extraction recipe using Python libraries
instead of PDAL, following the same processing steps:

1. Sanity-check the LAS file
2. Ground segmentation
3. Height-above-ground + intensity normalization
4. Bright-paint candidate mask
5. Denoise & thin
6. Vectorize lane stripes using RANSAC
7. Export results

Usage:
    python pdal_style_lane_extract.py --input input.las [--output lanes.geojson]
"""

import argparse
import numpy as np
import laspy
import open3d as o3d
import json
import geopandas as gpd
from shapely.geometry import LineString, Point
from sklearn.cluster import DBSCAN
from scipy.spatial.distance import cdist
from scipy.signal import savgol_filter
import matplotlib.pyplot as plt
from pathlib import Path
import sys

def inspect_las_file(las_path):
    """
    Step 1: Sanity-check the LAS file
    """
    print("="*60)
    print("STEP 1: LAS FILE INSPECTION")
    print("="*60)

    las = laspy.read(las_path)

    print(f"File: {las_path}")
    print(f"Points: {len(las.points):,}")
    print(f"LAS version: {las.header.version}")
    print(f"Point format: {las.header.point_format}")

    # Coordinate info
    points = las.xyz
    print(f"\nCoordinate ranges:")
    print(f"  X: {points[:, 0].min():.6f} to {points[:, 0].max():.6f}")
    print(f"  Y: {points[:, 1].min():.6f} to {points[:, 1].max():.6f}")
    print(f"  Z: {points[:, 2].min():.6f} to {points[:, 2].max():.6f}")

    # Scale and offset
    print(f"\nScale: {las.header.scale}")
    print(f"Offset: {las.header.offset}")

    # Check for required dimensions
    print(f"\nAvailable dimensions:")
    for dim in las.point_format.dimension_names:
        print(f"  - {dim}")

    # Intensity check
    if hasattr(las, 'intensity'):
        intensity = las.intensity
        print(f"\nIntensity statistics:")
        print(f"  Range: {intensity.min()} to {intensity.max()}")
        print(f"  Mean: {intensity.mean():.1f}")
        print(f"  Std: {intensity.std():.1f}")

        # Check for road-mark values
        high_intensity = np.sum(intensity > 150)
        print(f"  Points with intensity > 150: {high_intensity:,} ({100*high_intensity/len(intensity):.1f}%)")
    else:
        print("WARNING: No intensity dimension found!")
        return None

    return las

def ground_segmentation(las, slope_threshold=0.04, cell_size=1.0):
    """
    Step 2: Ground segmentation using Open3D
    """
    print("\n" + "="*60)
    print("STEP 2: GROUND SEGMENTATION")
    print("="*60)

    # Create point cloud
    points = las.xyz
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)

    # Segment ground plane using RANSAC
    plane_model, inliers = pcd.segment_plane(
        distance_threshold=0.5,
        ransac_n=3,
        num_iterations=1000
    )

    print(f"Ground plane model: {plane_model}")
    print(f"Ground points: {len(inliers):,} ({100*len(inliers)/len(points):.1f}%)")

    # Extract ground points
    ground_indices = np.array(inliers)
    ground_points = points[ground_indices]
    ground_intensity = las.intensity[ground_indices]

    return ground_points, ground_intensity, ground_indices

def height_above_ground_and_intensity_norm(ground_points, ground_intensity):
    """
    Step 3: Height-above-ground + intensity normalization
    """
    print("\n" + "="*60)
    print("STEP 3: HEIGHT ABOVE GROUND & INTENSITY NORMALIZATION")
    print("="*60)

    # For ground points, height above ground is essentially 0
    # But we can compute local height variations
    z_min = ground_points[:, 2].min()
    height_above_ground = ground_points[:, 2] - z_min

    # Intensity normalization (simplified - in real PDAL this uses return distance)
    # Here we'll use a simple normalization
    intensity_norm = ground_intensity.astype(float)

    print(f"Height above ground range: {height_above_ground.min():.3f} to {height_above_ground.max():.3f}")
    print(f"Intensity normalization applied")

    return height_above_ground, intensity_norm

def bright_paint_candidates(ground_points, intensity_norm, threshold=180):
    """
    Step 4: Bright-paint candidate mask
    """
    print("\n" + "="*60)
    print("STEP 4: BRIGHT PAINT CANDIDATE EXTRACTION")
    print("="*60)

    # Apply intensity threshold
    bright_mask = intensity_norm > threshold
    bright_points = ground_points[bright_mask]
    bright_intensity = intensity_norm[bright_mask]

    print(f"Intensity threshold: {threshold}")
    print(f"Bright paint candidates: {len(bright_points):,} ({100*len(bright_points)/len(ground_points):.1f}%)")

    if len(bright_points) == 0:
        print("WARNING: No bright paint candidates found! Try lowering the threshold.")
        return None, None

    return bright_points, bright_intensity

def denoise_and_thin(bright_points, bright_intensity, voxel_size=0.02):
    """
    Step 5: Denoise & thin using statistical outlier removal and voxel downsampling
    """
    print("\n" + "="*60)
    print("STEP 5: DENOISE & THIN")
    print("="*60)

    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(bright_points)

    # Statistical outlier removal
    pcd_clean, outlier_indices = pcd.remove_statistical_outlier(
        nb_neighbors=16,
        std_ratio=1.0
    )

    print(f"Outlier removal: {len(bright_points)} -> {len(pcd_clean.points)} points")

    # Voxel downsampling
    pcd_thin = pcd_clean.voxel_down_sample(voxel_size=voxel_size)

    print(f"Voxel downsampling ({voxel_size*100:.0f}cm): {len(pcd_clean.points)} -> {len(pcd_thin.points)} points")

    # Get final points and intensities
    final_points = np.asarray(pcd_thin.points)

    # Map intensities (simplified - in practice you'd track indices through the pipeline)
    if len(final_points) > 0:
        final_intensity = np.ones(len(final_points)) * bright_intensity.mean()
    else:
        final_intensity = np.array([])

    return final_points, final_intensity

def vectorize_lane_stripes_ransac(lane_points, min_points_per_line=50):
    """
    Step 6: Vectorize lane stripes using RANSAC line fitting
    """
    print("\n" + "="*60)
    print("STEP 6: VECTORIZE LANE STRIPES")
    print("="*60)

    if len(lane_points) < min_points_per_line:
        print("Not enough points for lane extraction")
        return []

    # Cluster points into potential lane segments using DBSCAN
    clustering = DBSCAN(eps=0.5, min_samples=min_points_per_line).fit(lane_points[:, :2])
    labels = clustering.labels_

    unique_labels = set(labels)
    if -1 in unique_labels:
        unique_labels.remove(-1)  # Remove noise label

    print(f"Found {len(unique_labels)} potential lane clusters")

    lane_lines = []

    for label in unique_labels:
        cluster_mask = labels == label
        cluster_points = lane_points[cluster_mask]

        if len(cluster_points) < min_points_per_line:
            continue

        # Use simple linear regression for line fitting instead of RANSAC
        try:
            # Extract 2D points
            points_2d = cluster_points[:, :2]

            # Fit line using least squares
            x_coords = points_2d[:, 0]
            y_coords = points_2d[:, 1]

            # Check if points form a reasonable line
            x_range = x_coords.max() - x_coords.min()
            y_range = y_coords.max() - y_coords.min()

            if x_range < 0.5 and y_range < 0.5:  # Too small to be a lane
                continue

            # Fit line: y = mx + b or x = my + b (choose based on which has larger range)
            if x_range >= y_range:
                # Fit y = mx + b
                A = np.vstack([x_coords, np.ones(len(x_coords))]).T
                m, b = np.linalg.lstsq(A, y_coords, rcond=None)[0]

                # Create line endpoints
                x_min, x_max = x_coords.min(), x_coords.max()
                y_min, y_max = m * x_min + b, m * x_max + b
                start_point = [x_min, y_min]
                end_point = [x_max, y_max]
            else:
                # Fit x = my + b
                A = np.vstack([y_coords, np.ones(len(y_coords))]).T
                m, b = np.linalg.lstsq(A, x_coords, rcond=None)[0]

                # Create line endpoints
                y_min, y_max = y_coords.min(), y_coords.max()
                x_min, x_max = m * y_min + b, m * y_max + b
                start_point = [x_min, y_min]
                end_point = [x_max, y_max]

            line = LineString([start_point, end_point])

            # Only keep lines that are reasonably long
            if line.length > 2.0:  # At least 2 meters long
                lane_lines.append({
                    'geometry': line,
                    'length': line.length,
                    'points_count': len(cluster_points),
                    'cluster_id': label
                })

                print(f"  Lane {label}: {len(cluster_points)} points, {line.length:.1f}m long")

        except Exception as e:
            print(f"  Failed to fit line for cluster {label}: {e}")
            continue

    return lane_lines

def export_results(lane_lines, output_path, crs="EPSG:32633"):
    """
    Step 7: Export results to GeoJSON
    """
    print("\n" + "="*60)
    print("STEP 7: EXPORT RESULTS")
    print("="*60)

    if not lane_lines:
        print("No lane lines to export")
        return

    # Create GeoDataFrame
    geometries = [line['geometry'] for line in lane_lines]
    properties = [{
        'length': line['length'],
        'points_count': line['points_count'],
        'cluster_id': line['cluster_id'],
        'lane_type': 'white_solid' if line['length'] > 4 else 'white_dashed'
    } for line in lane_lines]

    gdf = gpd.GeoDataFrame(properties, geometry=geometries, crs=crs)

    # Simplify geometries
    gdf['geometry'] = gdf['geometry'].simplify(0.05)

    # Export
    gdf.to_file(output_path, driver="GeoJSON")

    print(f"Exported {len(lane_lines)} lane lines to {output_path}")
    print(f"Total length: {gdf['length'].sum():.1f} meters")

def main():
    parser = argparse.ArgumentParser(description='PDAL-style lane extraction from LAS files')
    parser.add_argument('--input', required=True, help='Input LAS file path')
    parser.add_argument('--output', default='lane_markings.geojson', help='Output GeoJSON file path')
    parser.add_argument('--intensity-threshold', type=float, default=180, help='Intensity threshold for bright paint')
    parser.add_argument('--voxel-size', type=float, default=0.02, help='Voxel size for downsampling (meters)')

    args = parser.parse_args()

    # Check input file
    if not Path(args.input).exists():
        print(f"Error: Input file {args.input} not found")
        sys.exit(1)

    try:
        # Step 1: Inspect LAS file
        las = inspect_las_file(args.input)
        if las is None:
            sys.exit(1)

        # Step 2: Ground segmentation
        ground_points, ground_intensity, ground_indices = ground_segmentation(las)

        # Step 3: Height above ground & intensity normalization
        hag, intensity_norm = height_above_ground_and_intensity_norm(ground_points, ground_intensity)

        # Step 4: Bright paint candidates
        bright_points, bright_intensity = bright_paint_candidates(
            ground_points, intensity_norm, threshold=args.intensity_threshold
        )

        if bright_points is None:
            sys.exit(1)

        # Step 5: Denoise & thin
        lane_points, lane_intensity = denoise_and_thin(
            bright_points, bright_intensity, voxel_size=args.voxel_size
        )

        # Step 6: Vectorize lane stripes
        lane_lines = vectorize_lane_stripes_ransac(lane_points)

        # Step 7: Export results
        export_results(lane_lines, args.output)

        print("\n" + "="*60)
        print("LANE EXTRACTION COMPLETE!")
        print("="*60)

    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
