#!/usr/bin/env python3
"""
Test script for the comprehensive lane GeoJSON output format.
This script creates a simple test to verify the new comprehensive format works correctly.
"""

import json
import tempfile
import os
from pathlib import Path
import sys

# Add the lanes_compare directory to the path
sys.path.insert(0, str(Path(__file__).parent / "lanes_compare"))

try:
    from precise_las_to_lane_geojson import (
        create_comprehensive_geojson_output,
        create_lane_boundaries,
        create_individual_lanes,
        create_boundary_geometry,
        create_road_references,
        determine_lane_type,
        create_lane_attributes,
        create_parametric_lane_attributes
    )
    import geopandas as gpd
    import pandas as pd
    from shapely.geometry import LineString, Point
    print("✓ Successfully imported all functions")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

def create_test_data():
    """Create test lane data for testing."""
    # Create test geometries
    line1 = LineString([(0, 0), (1, 0), (2, 0)])
    line2 = LineString([(0, 0.003), (1, 0.003), (2, 0.003)])  # Offset by ~3 meters
    
    # Create test lane data
    test_data = [
        {
            'geometry': line1,
            'lane_index': 1,
            'highway': 'primary',
            'name': 'Test Road',
            'osmid': 12345,
            'lanes': 2,
            'oneway': 'no',
            'maxspeed': '50',
            'turn': 'through',
            'lane_position': 'right',
            'lane_width': 3.0,
            'road_class': 'primary'
        },
        {
            'geometry': line2,
            'lane_index': 2,
            'highway': 'primary',
            'name': 'Test Road',
            'osmid': 12345,
            'lanes': 2,
            'oneway': 'no',
            'maxspeed': '50',
            'turn': 'left',
            'lane_position': 'left',
            'lane_width': 3.0,
            'road_class': 'primary'
        }
    ]
    
    # Create GeoDataFrame
    gdf = gpd.GeoDataFrame(test_data, crs="EPSG:4326")
    return gdf

def test_comprehensive_output():
    """Test the comprehensive GeoJSON output."""
    print("\n🧪 Testing comprehensive GeoJSON output...")
    
    # Create test data
    lanes_gdf = create_test_data()
    markings_gdf = gpd.GeoDataFrame(geometry=[], crs="EPSG:4326")  # Empty markings
    
    # Create temporary output file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.geojson', delete=False) as f:
        temp_path = f.name
    
    try:
        # Test the comprehensive output function
        result_path = create_comprehensive_geojson_output(lanes_gdf, markings_gdf, temp_path)
        
        # Verify the file was created
        if not os.path.exists(result_path):
            print("✗ Output file was not created")
            return False
        
        # Load and verify the GeoJSON structure
        with open(result_path, 'r') as f:
            geojson_data = json.load(f)
        
        # Check basic structure
        if geojson_data.get('type') != 'FeatureCollection':
            print("✗ Invalid GeoJSON type")
            return False
        
        features = geojson_data.get('features', [])
        if not features:
            print("✗ No features found in output")
            return False
        
        # Check first feature structure
        feature = features[0]
        required_props = [
            '@ns:com:here:mom:meta',
            '@ns:com:here:xyz',
            'featureType',
            'laneBoundaries',
            'lanes',
            'referenceGeometry',
            'leftBoundaryGeometry',
            'rightBoundaryGeometry'
        ]
        
        props = feature.get('properties', {})
        missing_props = [prop for prop in required_props if prop not in props]
        
        if missing_props:
            print(f"✗ Missing required properties: {missing_props}")
            return False
        
        # Check lane boundaries structure
        lane_boundaries = props.get('laneBoundaries', [])
        if not lane_boundaries:
            print("✗ No lane boundaries found")
            return False
        
        boundary = lane_boundaries[0]
        if 'laneBoundaryId' not in boundary:
            print("✗ Lane boundary missing ID")
            return False
        
        # Check lanes structure
        lanes = props.get('lanes', [])
        if not lanes:
            print("✗ No lanes found")
            return False
        
        lane = lanes[0]
        required_lane_props = [
            'drivePathGeometry',
            'laneAttributes',
            'laneParametericAttributes',
            'directionOfTravel'
        ]
        
        missing_lane_props = [prop for prop in required_lane_props if prop not in lane]
        if missing_lane_props:
            print(f"✗ Missing required lane properties: {missing_lane_props}")
            return False
        
        print("✓ Comprehensive GeoJSON structure is valid")
        print(f"✓ Generated {len(features)} lane group feature(s)")
        print(f"✓ First feature has {len(lane_boundaries)} lane boundaries")
        print(f"✓ First feature has {len(lanes)} individual lanes")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        if os.path.exists(temp_path):
            os.unlink(temp_path)

def test_helper_functions():
    """Test individual helper functions."""
    print("\n🔧 Testing helper functions...")
    
    # Create test data
    lanes_gdf = create_test_data()
    test_lane = lanes_gdf.iloc[0]
    
    try:
        # Test determine_lane_type
        lane_type = determine_lane_type(test_lane, 'primary')
        if not isinstance(lane_type, dict) or 'regular' not in lane_type:
            print("✗ determine_lane_type failed")
            return False
        print("✓ determine_lane_type works")
        
        # Test create_lane_attributes
        attrs = create_lane_attributes(test_lane, 'primary')
        if not isinstance(attrs, dict) or 'laneTypes' not in attrs:
            print("✗ create_lane_attributes failed")
            return False
        print("✓ create_lane_attributes works")
        
        # Test create_parametric_lane_attributes
        param_attrs = create_parametric_lane_attributes(test_lane, 'primary')
        if not isinstance(param_attrs, dict) or 'parametricLaneType' not in param_attrs:
            print("✗ create_parametric_lane_attributes failed")
            return False
        print("✓ create_parametric_lane_attributes works")
        
        # Test create_road_references
        road_refs = create_road_references(test_lane, 12345)
        if not isinstance(road_refs, list) or not road_refs:
            print("✗ create_road_references failed")
            return False
        print("✓ create_road_references works")
        
        # Test create_boundary_geometry
        boundary_geom = create_boundary_geometry(lanes_gdf, 'left')
        if not isinstance(boundary_geom, dict) or 'coordinates' not in boundary_geom:
            print("✗ create_boundary_geometry failed")
            return False
        print("✓ create_boundary_geometry works")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing helper functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting comprehensive lane GeoJSON tests...")
    
    success = True
    
    # Test helper functions
    if not test_helper_functions():
        success = False
    
    # Test comprehensive output
    if not test_comprehensive_output():
        success = False
    
    if success:
        print("\n🎉 All tests passed! The comprehensive lane format is working correctly.")
        print("\nThe script now generates GeoJSON with:")
        print("  • Detailed lane group features")
        print("  • Individual lane boundaries with attributes")
        print("  • Lane-specific properties and geometries")
        print("  • OSM-compatible metadata")
        print("  • Comprehensive lane type information")
        print("  • Parametric lane attributes")
        print("  • Reference geometries and boundary geometries")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
