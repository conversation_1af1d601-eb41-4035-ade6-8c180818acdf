#!/usr/bin/env python3
"""
3D Visualization of Lane Extraction Results
===========================================

This script provides 3D visualization of the lane extraction pipeline results
using Open3D for interactive viewing.

Usage:
    python visualize_lane_results.py --las input.las --geojson lanes.geojson
"""

import argparse
import numpy as np
import laspy
import open3d as o3d
import geopandas as gpd
from pathlib import Path
import matplotlib.pyplot as plt

def load_and_normalize_las(las_path, sample_rate=0.1):
    """Load and normalize LAS file coordinates"""
    print(f"Loading LAS file: {las_path}")
    las = laspy.read(las_path)
    
    # Sample points for performance
    num_points = len(las.points)
    if sample_rate < 1.0:
        sample_size = int(num_points * sample_rate)
        indices = np.random.choice(num_points, sample_size, replace=False)
        points = las.xyz[indices]
        intensity = las.intensity[indices] if hasattr(las, 'intensity') else np.zeros(sample_size)
    else:
        points = las.xyz
        intensity = las.intensity if hasattr(las, 'intensity') else np.zeros(len(points))
    
    print(f"Using {len(points):,} points ({sample_rate*100:.1f}% of total)")
    
    # Normalize coordinates around center
    center = np.mean(points, axis=0)
    points_normalized = points - center
    
    print(f"Coordinate center: {center}")
    print(f"Normalized bounds:")
    print(f"  X: {points_normalized[:, 0].min():.2f} to {points_normalized[:, 0].max():.2f}")
    print(f"  Y: {points_normalized[:, 1].min():.2f} to {points_normalized[:, 1].max():.2f}")
    print(f"  Z: {points_normalized[:, 2].min():.2f} to {points_normalized[:, 2].max():.2f}")
    
    return points_normalized, intensity, center

def load_lane_lines(geojson_path, center):
    """Load lane lines from GeoJSON and normalize coordinates"""
    if not Path(geojson_path).exists():
        print(f"Warning: GeoJSON file {geojson_path} not found")
        return []
    
    print(f"Loading lane lines: {geojson_path}")
    gdf = gpd.read_file(geojson_path)
    
    lane_lines = []
    for idx, row in gdf.iterrows():
        geom = row.geometry
        if geom.geom_type == 'LineString':
            coords = np.array(geom.coords)
            # Add Z coordinate (assume ground level)
            if coords.shape[1] == 2:
                coords = np.column_stack([coords, np.zeros(len(coords))])
            
            # Normalize coordinates
            coords_normalized = coords - center
            lane_lines.append({
                'coords': coords_normalized,
                'properties': row.to_dict()
            })
    
    print(f"Loaded {len(lane_lines)} lane lines")
    return lane_lines

def create_point_cloud_visualization(points, intensity):
    """Create colored point cloud for visualization"""
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # Color by intensity
    if len(intensity) > 0:
        intensity_normalized = (intensity - intensity.min()) / (intensity.max() - intensity.min())
        colors = plt.cm.viridis(intensity_normalized)[:, :3]
        pcd.colors = o3d.utility.Vector3dVector(colors)
    else:
        # Default gray color
        colors = np.ones((len(points), 3)) * 0.7
        pcd.colors = o3d.utility.Vector3dVector(colors)
    
    return pcd

def create_lane_line_visualization(lane_lines):
    """Create line geometries for lane visualization"""
    line_sets = []
    colors = [
        [1, 0, 0],    # Red
        [0, 1, 0],    # Green
        [0, 0, 1],    # Blue
        [1, 1, 0],    # Yellow
        [1, 0, 1],    # Magenta
        [0, 1, 1],    # Cyan
        [1, 0.5, 0],  # Orange
        [0.5, 0, 1],  # Purple
    ]
    
    for i, lane in enumerate(lane_lines):
        coords = lane['coords']
        if len(coords) < 2:
            continue
        
        # Create line set
        line_set = o3d.geometry.LineSet()
        line_set.points = o3d.utility.Vector3dVector(coords)
        
        # Create line indices
        lines = [[j, j+1] for j in range(len(coords)-1)]
        line_set.lines = o3d.utility.Vector2iVector(lines)
        
        # Set color
        color = colors[i % len(colors)]
        line_colors = [color for _ in lines]
        line_set.colors = o3d.utility.Vector3dVector(line_colors)
        
        line_sets.append(line_set)
        
        # Print lane info
        props = lane['properties']
        length = props.get('length', 0)
        lane_type = props.get('lane_type', 'unknown')
        print(f"  Lane {i}: {length:.1f}m, type: {lane_type}")
    
    return line_sets

def visualize_ground_segmentation(points, intensity, ground_threshold=0.5):
    """Visualize ground segmentation results"""
    print("Performing ground segmentation for visualization...")
    
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # Segment ground plane
    plane_model, inliers = pcd.segment_plane(
        distance_threshold=ground_threshold,
        ransac_n=3,
        num_iterations=1000
    )
    
    print(f"Ground plane: {plane_model}")
    print(f"Ground points: {len(inliers):,}")
    
    # Create separate point clouds for ground and non-ground
    ground_pcd = pcd.select_by_index(inliers)
    non_ground_pcd = pcd.select_by_index(inliers, invert=True)
    
    # Color ground points green, non-ground points brown
    ground_pcd.paint_uniform_color([0.2, 0.8, 0.2])  # Green
    non_ground_pcd.paint_uniform_color([0.6, 0.4, 0.2])  # Brown
    
    return ground_pcd, non_ground_pcd

def main():
    parser = argparse.ArgumentParser(description='Visualize lane extraction results in 3D')
    parser.add_argument('--las', required=True, help='Input LAS file path')
    parser.add_argument('--geojson', help='Lane lines GeoJSON file path')
    parser.add_argument('--sample-rate', type=float, default=0.1, help='Point cloud sampling rate')
    parser.add_argument('--show-ground-seg', action='store_true', help='Show ground segmentation')
    parser.add_argument('--intensity-threshold', type=float, default=180, help='Intensity threshold for highlighting')
    
    args = parser.parse_args()
    
    # Check input file
    if not Path(args.las).exists():
        print(f"Error: LAS file {args.las} not found")
        return
    
    try:
        # Load and normalize point cloud
        points, intensity, center = load_and_normalize_las(args.las, args.sample_rate)
        
        geometries = []
        
        if args.show_ground_seg:
            # Show ground segmentation
            ground_pcd, non_ground_pcd = visualize_ground_segmentation(points, intensity)
            geometries.extend([ground_pcd, non_ground_pcd])
        else:
            # Show intensity-colored point cloud
            pcd = create_point_cloud_visualization(points, intensity)
            
            # Highlight high-intensity points
            if len(intensity) > 0:
                high_intensity_mask = intensity > args.intensity_threshold
                if np.any(high_intensity_mask):
                    high_intensity_points = points[high_intensity_mask]
                    high_pcd = o3d.geometry.PointCloud()
                    high_pcd.points = o3d.utility.Vector3dVector(high_intensity_points)
                    high_pcd.paint_uniform_color([1, 1, 0])  # Yellow for high intensity
                    geometries.append(high_pcd)
                    
                    print(f"High intensity points (>{args.intensity_threshold}): {len(high_intensity_points):,}")
            
            geometries.append(pcd)
        
        # Load and visualize lane lines if provided
        if args.geojson:
            lane_lines = load_lane_lines(args.geojson, center)
            line_sets = create_lane_line_visualization(lane_lines)
            geometries.extend(line_sets)
        
        # Add coordinate frame
        coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=10.0)
        geometries.append(coord_frame)
        
        # Visualize
        print("\nOpening 3D viewer...")
        print("Controls:")
        print("- Mouse: Rotate view")
        print("- Mouse wheel: Zoom")
        print("- Ctrl + Mouse: Pan")
        print("- Press 'Q' to quit")
        
        o3d.visualization.draw_geometries(
            geometries,
            window_name="Lane Extraction Results",
            width=1200,
            height=800,
            left=50,
            top=50
        )
        
    except Exception as e:
        print(f"Error during visualization: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
