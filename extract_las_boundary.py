#!/usr/bin/env python3
"""
Extract the exact boundary of a LAS file and save it as GeoJSON.
This helps visualize the precise area covered by the LAS data.
"""

import json
import sys
from pathlib import Path
import geopandas as gpd

# Add the lanes_compare directory to the path
sys.path.insert(0, str(Path(__file__).parent / "lanes_compare"))

from precise_las_to_lane_geojson import extract_las_boundary_polygon, convert_polygon_to_wgs84

def extract_and_save_boundary(las_path: str, output_path: str, utm_epsg: int = 32633):
    """Extract LAS boundary and save as GeoJSON."""
    print(f"Extracting boundary from: {las_path}")
    
    # Extract boundary polygon
    boundary_polygon, bounds = extract_las_boundary_polygon(las_path, sample_rate=0.02, alpha=50.0)
    
    # Convert to WGS84
    wgs84_polygon, wgs84_bounds = convert_polygon_to_wgs84(boundary_polygon, utm_epsg)
    
    # Create GeoDataFrame
    gdf = gpd.GeoDataFrame(
        {
            'name': ['LAS File Boundary'],
            'las_file': [Path(las_path).name],
            'utm_epsg': [utm_epsg],
            'area_sqm': [boundary_polygon.area],
            'min_x': [bounds[0]],
            'min_y': [bounds[1]], 
            'max_x': [bounds[2]],
            'max_y': [bounds[3]],
            'min_lon': [wgs84_bounds[0]],
            'min_lat': [wgs84_bounds[1]],
            'max_lon': [wgs84_bounds[2]],
            'max_lat': [wgs84_bounds[3]]
        },
        geometry=[wgs84_polygon],
        crs="EPSG:4326"
    )
    
    # Save as GeoJSON
    gdf.to_file(output_path, driver="GeoJSON")
    
    print(f"Boundary saved to: {output_path}")
    print(f"Area: {boundary_polygon.area:.1f} square meters")
    print(f"WGS84 bounds: ({wgs84_bounds[0]:.6f}, {wgs84_bounds[1]:.6f}) to ({wgs84_bounds[2]:.6f}, {wgs84_bounds[3]:.6f})")
    
    return output_path

def main():
    """Extract boundary for the HT412 LAS file."""
    las_file = "lanes_compare/HT412_1738935654_3217135_1422974157258029_1422974185645127.las"
    output_file = "lanes_compare/HT412_boundary.geojson"
    
    if not Path(las_file).exists():
        print(f"Error: LAS file not found: {las_file}")
        sys.exit(1)
    
    extract_and_save_boundary(las_file, output_file, utm_epsg=32633)
    
    # Load and display some info about the boundary
    with open(output_file, 'r') as f:
        data = json.load(f)
    
    feature = data['features'][0]
    props = feature['properties']
    
    print(f"\nBoundary Information:")
    print(f"  LAS File: {props['las_file']}")
    print(f"  Area: {props['area_sqm']:.1f} square meters")
    print(f"  UTM Bounds: ({props['min_x']:.1f}, {props['min_y']:.1f}) to ({props['max_x']:.1f}, {props['max_y']:.1f})")
    print(f"  WGS84 Bounds: ({props['min_lon']:.6f}, {props['min_lat']:.6f}) to ({props['max_lon']:.6f}, {props['max_lat']:.6f})")
    print(f"  Boundary vertices: {len(feature['geometry']['coordinates'][0])}")

if __name__ == "__main__":
    main()
