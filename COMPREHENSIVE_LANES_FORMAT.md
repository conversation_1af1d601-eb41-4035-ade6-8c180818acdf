# Comprehensive Lane GeoJSON Format

## Overview

The `precise_las_to_lane_geojson.py` script has been updated to generate GeoJSON output in a comprehensive format that matches the detailed lane-level mapping structure you specified. This format is designed to be compatible with OSM data while providing extensive lane properties, boundaries, and attributes.

## Key Features

### 🎯 **Comprehensive Lane Group Structure**
- Each feature represents a complete lane group (road segment)
- Includes detailed lane boundaries with confidence scores
- Individual lanes with drive path geometries
- Reference geometries and boundary geometries
- OSM-compatible metadata and properties

### 🛣️ **Detailed Lane Properties**
- Lane-specific attributes (types, widths, speeds, etc.)
- Parametric lane attributes for advanced mapping
- Lane boundary attributes with traversal information
- Direction and turn lane information
- Speed limits and road surface data

### 📍 **Precise Geometry Handling**
- Uses exact polygon boundaries from LAS point cloud data
- Proper lane offset calculations based on road type
- Left and right boundary geometries
- Drive path geometries for each individual lane

## Generated GeoJSON Structure

The output follows this comprehensive structure:

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "id": "laneGroup_[road_id]_[uuid]",
      "momType": "laneGroup",
      "referencePoint": {
        "type": "Point",
        "coordinates": [lon, lat]
      },
      "geometry": {
        "type": "LineString", 
        "coordinates": [[lon, lat], ...]
      },
      "properties": {
        "@ns:com:here:mom:meta": {
          "modelVersion": "1.0",
          "sourceId": "osm_las_processor",
          "lastUpdatedBy": "precise_las_to_lane_geojson",
          "updatedByUser": "system"
        },
        "@ns:com:here:xyz": {
          "tags": ["highway_type", "lane_group", "osm_derived"]
        },
        "featureType": "laneGroup",
        "lengthInCm": 12345,
        "startLaneGroupConnectorId": "connector_start_[id]",
        "endLaneGroupConnectorId": "connector_end_[id]",
        "referenceGeometry": { ... },
        "leftBoundaryGeometry": { ... },
        "rightBoundaryGeometry": { ... },
        "laneBoundaries": [
          {
            "laneBoundaryId": "boundary_left_[lane]_[uuid]",
            "geometry": { "type": "LineString", "coordinates": [...] },
            "confidence": {
              "simpleScores": [{
                "scoreType": "detection",
                "score": 0.85,
                "featureType": "laneBoundary",
                "updateSource": "osm_derived"
              }]
            },
            "laneBoundaryAttributes": {
              "roadBoundaryType": [{
                "roadBoundaryType": "solid|dashed",
                "boundaryRange": { "startOffset": 0, "endOffset": 100 }
              }],
              "laneBoundaryTraversal": [{ ... }],
              "adjacentLaneGroups": []
            },
            "parallelElements": {
              "sequentialElements": [{
                "range": { "startOffset": 0, "endOffset": 100 },
                "stripeDetail": {
                  "color": "white",
                  "material": "paint", 
                  "styleType": "solid|dashed",
                  "widthInCentimeters": 10
                },
                "laneBoundaryTraversal": "allowed"
              }]
            }
          }
        ],
        "lanes": [
          {
            "startLaneConnectorId": "connector_start_lane_[id]",
            "endLaneConnectorId": "connector_end_lane_[id]",
            "leftLaneBoundaryId": "boundary_left_[lane]",
            "rightLaneBoundaryId": "boundary_right_[lane]",
            "lengthInCm": 12345,
            "directionOfTravel": "forward|backward",
            "drivePathGeometry": {
              "type": "LineString",
              "coordinates": [[lon, lat], ...]
            },
            "laneAttributes": {
              "laneTypes": [{
                "laneRange": { "startOffset": 0, "endOffset": 100 },
                "laneType": {
                  "regular": true,
                  "auxiliary": false,
                  "express": false,
                  "turn": false,
                  // ... all lane type flags
                }
              }],
              "laneWidthProfile": {
                "startWidthCm": 350,
                "endWidthCm": 350,
                "minWidthCm": 350,
                "maxWidthCm": 350,
                "minWidthLocation": 0,
                "maxWidthLocation": 0
              },
              "speedLimits": [{
                "laneRange": { "startOffset": 0, "endOffset": 100 },
                "value": 50,
                "unit": "kmh",
                "isUnlimited": false
              }],
              "directionCategoryMarkers": [{ ... }],
              "transitions": [],
              "intersections": [],
              "laneAccesses": [],
              "stoppingLocations": []
            },
            "laneParametericAttributes": {
              "parametricLaneType": [{ ... }],
              "laneWidthProfile": { ... },
              "parametricSpeedLimit": [],
              "parametricTransitionStatus": []
            },
            "roadReferences": [{
              "topologySegmentRef": {
                "id": "segment_[road_id]",
                "referencePoint": { "type": "Point", "coordinates": [...] }
              },
              "topologySegmentRange": { "startOffset": 0, "endOffset": 100 },
              "sourceRange": { "startOffset": 0, "endOffset": 100 }
            }]
          }
        ],
        "incomingLaneGroups": [],
        "outgoingLaneGroups": [],
        "roadReferences": [{ ... }]
      }
    }
  ]
}
```

## Usage

### Basic Usage
```bash
python lanes_compare/precise_las_to_lane_geojson.py --las data.las --utm_epsg 32632
```

### Advanced Usage
```bash
python lanes_compare/precise_las_to_lane_geojson.py \
  --las data.las \
  --utm_epsg 32632 \
  --output comprehensive_lanes.geojson \
  --sample_rate 0.02 \
  --alpha 150
```

### Output Files
The script now generates files with the suffix `_comprehensive_lanes_full.geojson` to indicate the comprehensive format.

## Key Improvements

1. **OSM Compatibility**: Full integration with OpenStreetMap data and attributes
2. **Detailed Lane Boundaries**: Each lane boundary includes confidence scores, attributes, and styling information
3. **Individual Lane Features**: Each lane has its own drive path geometry and detailed attributes
4. **Comprehensive Metadata**: Includes HERE-style metadata for professional mapping applications
5. **Lane Type Classification**: Automatic classification based on highway type and OSM attributes
6. **Parametric Attributes**: Advanced lane attributes for sophisticated mapping applications

## Testing

Run the test suite to verify functionality:
```bash
python test_comprehensive_lanes.py
```

Generate a sample output:
```bash
python generate_sample_comprehensive.py
```

## Integration with Existing Workflow

This comprehensive format maintains compatibility with your existing LAS processing workflow while providing the detailed structure you requested. The script:

- Uses the same precise LAS boundary extraction
- Integrates with OSM data using the same query methods
- Maintains the same command-line interface
- Outputs to the same directory structure

The comprehensive format is now ready for use with your map API integration and EC2 deployment workflow.
