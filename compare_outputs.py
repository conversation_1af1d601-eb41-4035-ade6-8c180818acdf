#!/usr/bin/env python3
"""
Compare the different GeoJSON outputs to show improvements.
"""

import json
from pathlib import Path

def analyze_geojson(file_path):
    """Analyze a GeoJSON file and return statistics."""
    if not Path(file_path).exists():
        return None
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    stats = {
        'file_size': Path(file_path).stat().st_size,
        'features': len(data.get('features', [])),
        'feature_types': set(),
        'total_lanes': 0,
        'total_boundaries': 0,
        'highway_types': set(),
        'has_comprehensive_structure': False
    }
    
    for feature in data.get('features', []):
        props = feature.get('properties', {})
        
        # Check feature type
        if 'featureType' in props:
            stats['feature_types'].add(props['featureType'])
            stats['has_comprehensive_structure'] = True
        elif 'highway' in props:
            stats['feature_types'].add('highway')
        elif 'layer' in props:
            stats['feature_types'].add(props['layer'])
        
        # Count lanes and boundaries
        if 'lanes' in props and isinstance(props['lanes'], list):
            stats['total_lanes'] += len(props['lanes'])
        elif 'lane_index' in props:
            stats['total_lanes'] += 1
            
        if 'laneBoundaries' in props and isinstance(props['laneBoundaries'], list):
            stats['total_boundaries'] += len(props['laneBoundaries'])
        
        # Highway types
        if 'highway' in props:
            stats['highway_types'].add(props['highway'])
        
        # Check for comprehensive structure
        if '@ns:com:here:mom:meta' in props:
            stats['has_comprehensive_structure'] = True
    
    return stats

def main():
    """Compare all available outputs."""
    files_to_compare = [
        ("Original v1", "lanes_compare/HT412_comprehensive_lanes_full.geojson"),
        ("Improved v2", "lanes_compare/HT412_comprehensive_lanes_full_v2.geojson"),
        ("LAS Boundary", "lanes_compare/HT412_boundary.geojson")
    ]
    
    print("🔍 Comprehensive Lane GeoJSON Comparison")
    print("=" * 60)
    
    for name, file_path in files_to_compare:
        print(f"\n📁 {name}: {Path(file_path).name}")
        print("-" * 40)
        
        stats = analyze_geojson(file_path)
        if stats is None:
            print("   ❌ File not found")
            continue
        
        print(f"   📊 File size: {stats['file_size']:,} bytes ({stats['file_size']/1024:.1f} KB)")
        print(f"   🎯 Features: {stats['features']}")
        print(f"   🛣️  Total lanes: {stats['total_lanes']}")
        print(f"   📏 Total boundaries: {stats['total_boundaries']}")
        print(f"   🏷️  Feature types: {', '.join(stats['feature_types'])}")
        print(f"   🛤️  Highway types: {', '.join(stats['highway_types']) if stats['highway_types'] else 'None'}")
        print(f"   ✨ Comprehensive structure: {'Yes' if stats['has_comprehensive_structure'] else 'No'}")
    
    print(f"\n🎉 Summary of Improvements:")
    print("=" * 60)
    
    v1_stats = analyze_geojson(files_to_compare[0][1])
    v2_stats = analyze_geojson(files_to_compare[1][1])
    
    if v1_stats and v2_stats:
        print(f"✅ File size increased: {v1_stats['file_size']:,} → {v2_stats['file_size']:,} bytes")
        print(f"✅ Lanes increased: {v1_stats['total_lanes']} → {v2_stats['total_lanes']}")
        print(f"✅ Boundaries increased: {v1_stats['total_boundaries']} → {v2_stats['total_boundaries']}")
        print(f"✅ Better boundary detection with {v2_stats['file_size'] - v1_stats['file_size']:,} bytes more data")
    
    print(f"\n📋 Key Features of the Comprehensive Format:")
    print("=" * 60)
    print("✅ Lane group features with detailed metadata")
    print("✅ Individual lane boundaries with confidence scores")
    print("✅ Drive path geometries for each lane")
    print("✅ Comprehensive lane attributes (types, widths, speeds)")
    print("✅ Parametric lane attributes for advanced mapping")
    print("✅ OSM-compatible structure with HERE-style metadata")
    print("✅ Precise LAS boundary matching")
    print("✅ Reference and boundary geometries")
    print("✅ Lane boundary traversal and styling information")
    
    print(f"\n🗺️  Coordinate Information:")
    print("=" * 60)
    boundary_stats = analyze_geojson(files_to_compare[2][1])
    if boundary_stats:
        with open(files_to_compare[2][1], 'r') as f:
            boundary_data = json.load(f)
        props = boundary_data['features'][0]['properties']
        print(f"📍 LAS Area: {props['area_sqm']:.1f} square meters")
        print(f"📍 UTM Zone: {props['utm_epsg']} (EPSG:{props['utm_epsg']})")
        print(f"📍 WGS84 Bounds: ({props['min_lon']:.6f}, {props['min_lat']:.6f}) to ({props['max_lon']:.6f}, {props['max_lat']:.6f})")
        print(f"📍 Location: Poland (near coordinates 19.55°E, 52.34°N)")

if __name__ == "__main__":
    main()
