#!/usr/bin/env python3
"""
Generate a sample comprehensive lane GeoJSON to demonstrate the new format.
"""

import json
import sys
from pathlib import Path

# Add the lanes_compare directory to the path
sys.path.insert(0, str(Path(__file__).parent / "lanes_compare"))

from precise_las_to_lane_geojson import create_comprehensive_geojson_output
import geopandas as gpd
from shapely.geometry import LineString

def create_sample_data():
    """Create sample lane data for demonstration."""
    # Create sample geometries representing a 4-lane highway
    base_coords = [(0, 0), (0.01, 0), (0.02, 0)]  # ~2km highway segment
    
    lanes_data = []
    for i in range(4):
        # Offset each lane by ~3.5 meters (converted to degrees approximately)
        offset = i * 0.00003  # Rough conversion for demo
        coords = [(x, y + offset) for x, y in base_coords]
        
        lane_data = {
            'geometry': LineString(coords),
            'lane_index': i + 1,
            'highway': 'motorway',
            'name': 'Sample Highway',
            'osmid': 999999,
            'lanes': 4,
            'oneway': 'yes',
            'maxspeed': '120',
            'turn': 'through' if i < 3 else 'left',
            'lane_position': 'right',
            'lane_width': 3.5,
            'road_class': 'motorway',
            'surface': 'asphalt'
        }
        lanes_data.append(lane_data)
    
    return gpd.GeoDataFrame(lanes_data, crs="EPSG:4326")

def main():
    """Generate sample comprehensive GeoJSON."""
    print("Generating sample comprehensive lane GeoJSON...")
    
    # Create sample data
    lanes_gdf = create_sample_data()
    markings_gdf = gpd.GeoDataFrame(geometry=[], crs="EPSG:4326")
    
    # Generate output
    output_path = "sample_comprehensive_lanes.geojson"
    result_path = create_comprehensive_geojson_output(lanes_gdf, markings_gdf, output_path)
    
    print(f"Sample comprehensive GeoJSON saved to: {result_path}")
    
    # Load and display a portion of the structure
    with open(result_path, 'r') as f:
        data = json.load(f)
    
    print("\nSample structure preview:")
    print(f"Type: {data['type']}")
    print(f"Features: {len(data['features'])}")
    
    if data['features']:
        feature = data['features'][0]
        props = feature['properties']
        
        print(f"\nFirst feature properties:")
        print(f"  Feature Type: {props.get('featureType')}")
        print(f"  Lane Boundaries: {len(props.get('laneBoundaries', []))}")
        print(f"  Individual Lanes: {len(props.get('lanes', []))}")
        print(f"  Length (cm): {props.get('lengthInCm')}")
        
        # Show lane boundary structure
        if props.get('laneBoundaries'):
            boundary = props['laneBoundaries'][0]
            print(f"\nFirst lane boundary:")
            print(f"  ID: {boundary.get('laneBoundaryId')}")
            print(f"  Geometry type: {boundary.get('geometry', {}).get('type')}")
            print(f"  Has confidence scores: {'confidence' in boundary}")
            print(f"  Has attributes: {'laneBoundaryAttributes' in boundary}")
        
        # Show individual lane structure
        if props.get('lanes'):
            lane = props['lanes'][0]
            print(f"\nFirst individual lane:")
            print(f"  Direction: {lane.get('directionOfTravel')}")
            print(f"  Length (cm): {lane.get('lengthInCm')}")
            print(f"  Has drive path: {'drivePathGeometry' in lane}")
            print(f"  Has attributes: {'laneAttributes' in lane}")
            print(f"  Has parametric attributes: {'laneParametericAttributes' in lane}")

if __name__ == "__main__":
    main()
